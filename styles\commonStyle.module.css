.normalText {
  color: #515151;
}

.blueText {
  color: #1b84ff;
}

.lightText {
  color: #515151cc;
}

.blueLightText {
  color: #67748e;
}

.homeBlur {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #fff;
}

.homeBackground {
  background-color: #1b84ff0d;
  width: 100%;
  height: 100%;
}

.blurImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blurOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.centerImage {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: auto;
  z-index: 1;
}

.chatCard {
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  height: 85%;
  background: #fff;
  border-radius: 20px;
}

.conversationContainer {
  width: 35%;
  border: 1px solid #5151511a;
  border-radius: 20px;
  /* padding: 2rem; */
}

.chatRightContainer {
  border-radius: 0 !important;
  border-radius: 20px !important;
  background-color: transparent !important;
  border: 1px solid #5151511a !important;
}

.messageInput {
  background-color: transparent !important;
  width: 100%;
  border: none !important;
  color: #fff !important;
}

.messageInput:focus {
  border-color: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.messageInputContainer {
  display: flex;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
}

.sendIcon {
  position: absolute;
  right: 1rem;
  top: 1rem;
}

.chatReceive {
  background-color: #3b3a32;
  color: #fff;
  border-radius: 20px;
  border: 1px solid #ffffff1a;
  padding: 1rem;
  width: 60%;
}

.chatSend {
  background-color: #182427;
  color: #fff;
  border-radius: 20px;
  border: 1px solid #ffffff1a;
  padding: 1rem;
  width: 60%;
  margin-left: auto;
}

.chatMessages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem 0;
}

.micButton {
  background-color: transparent !important;
  border: none !important;
}

.micButton:hover:not(:disabled) {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}

.micButton:hover {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}

.micButton:focus {
  box-shadow: none !important;
  background-color: transparent !important;
  border: none !important;
}

.micButtonDenied {
  border: 2px solid #ef4444 !important;
  background-color: rgba(239, 68, 68, 0.1) !important;
}

.micButtonPending {
  border: 2px solid #f59e0b !important;
  background-color: rgba(245, 158, 11, 0.1) !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.interruptButton {
  border: 1px solid #5151511a !important;
  background-color: #ffffff !important;
  color: #515151 !important;
}

.interruptButton:focus {
  box-shadow: none !important;
  outline: none !important;
}

.interruptButton:hover:not(:disabled) {
  box-shadow: none !important;
  border: 1px solid #5151511a !important;
  background-color: #ffffff !important;
}

.interruptButton:hover {
  box-shadow: none !important;
  border: 1px solid #5151511a !important;
  background-color: #ffffff !important;
}

.videoContainer {
  width: 100%;
  height: 70%;
  object-fit: contain;
  z-index: 1;
  position: relative;
  display: flex;
  margin-right: auto;
}

.imageInput {
  position: absolute;
  top: 57%;
  left: 1rem;
}

.dashboardLeft {
  width: 20%;
  height: 100%;
  border-right: 1px solid #51515126;
  background-color: #fff;
  top: -2rem;
  left: -2rem;
}

.pageCard {
  border: 1px solid #51515126;
  border-radius: 30px;
  background-color: #fff;
}

.pageGreyCard {
  border: 1px solid #51515126;
  border-radius: 30px;
  background-color: #5151510d;
}

.headCard {
  background-color: #eeeeee;
  color: #515151;
  font-weight: 700;
  padding: 1rem;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  border-bottom: 1px solid #51515126;
  font-size: 16px;
}

.statusYellow {
  background-color: #f7e2491a;
  border: 1px solid #f7e249;
  border-radius: 20px;
  width: auto;
}

.statusGreen {
  background-color: #04cb121a;
  border: 1px solid #04cb12;
  border-radius: 20px;
  width: 11vw;
}

.statusBlue {
  background-color: #1b84ff1a;
  border: 1px solid #1b84ff;
  border-radius: 20px;
  width: auto;
}

.yellowStatus {
  background-color: #fffdef;
  border: 1px solid #ffde05;
  border-radius: 20px;
  color: #515151;
  font-size: 10px;
  max-width: 4rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.tableHead {
  background-color: #eeeeee;
  color: #515151;
  padding: 1rem;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  font-size: 16px;
}

.tabContainer {
  border-bottom: 1px solid #5151511a;
  display: flex;
  gap: 1.5rem;
  width: 100%;
}

.tab {
  color: #515151;
  cursor: pointer;
}

.activeTab {
  color: #1b84ff;
  border-bottom: 1px solid #1b84ff;
  cursor: pointer;
}

.tab:active {
  color: #1b84ff;
  border-bottom: 1px solid #1b84ff;
  cursor: pointer;
}

.circleOuterGray {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #515151;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circleOuterBlue {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #1b84ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circleInner {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #fff;
}

.imageOverlayText {
  position: absolute;
  bottom: 1vw;
  left: 5vw;
  transform: translateX(-50%);
  color: white;
  font-weight: bold;
  font-size: 1rem;
}

.errorBox {
  background-color: #c80a0a0d;
  border-radius: 10px;
  padding: 1rem;
  margin: 0 3rem;
  color: #c80a0a;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.textTruncate {
  max-width: 8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}