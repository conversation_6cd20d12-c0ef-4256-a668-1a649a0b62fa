/* PrimeReact Theme - Using light theme to match Figma */
@import "primereact/resources/themes/saga-blue/theme.css";
@import "primereact/resources/primereact.min.css";
@import "primeicons/primeicons.css";
@import "primeflex/primeflex.css";

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: var(--bg-primary);
}

/* Enhanced Typography Classes */
.text-display-large {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.text-display-medium {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.text-heading-large {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.text-heading-medium {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.text-heading-small {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-body-large {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
}

.text-body-medium {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-body-small {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

.text-overline {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--text-secondary);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Enhanced Design System - Colors, Typography, and Spacing */
:root {
  /* Color System - Following Material Design and WCAG Guidelines */
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;
  --primary-600: #1e88e5;
  --primary-700: #515151;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;

  --primary-color: var(--primary-600);
  --primary-color-hover: var(--primary-700);
  --primary-color-light: var(--primary-100);
  --primary-color-dark: var(--primary-800);

  /* Semantic Colors */
  --success-color: #4caf50;
  --success-light: #c8e6c9;
  --warning-color: #ff9800;
  --warning-light: #ffe0b2;
  --error-color: #f44336;
  --error-light: #ffcdd2;
  --info-color: #2196f3;
  --info-light: #bbdefb;

  /* Neutral Colors */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-disabled: var(--gray-500);
  --text-hint: var(--gray-600);

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-overlay: rgba(0, 0, 0, 0.6);

  /* Border Colors */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-lg: 1.125rem;
  /* 18px */
  --font-size-xl: 1.25rem;
  /* 20px */
  --font-size-2xl: 1.5rem;
  /* 24px */
  --font-size-3xl: 1.875rem;
  /* 30px */
  --font-size-4xl: 2.25rem;
  /* 36px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale (8px base) */
  --space-1: 0.25rem;
  /* 4px */
  --space-2: 0.5rem;
  /* 8px */
  --space-3: 0.75rem;
  /* 12px */
  --space-4: 1rem;
  /* 16px */
  --space-5: 1.25rem;
  /* 20px */
  --space-6: 1.5rem;
  /* 24px */
  --space-8: 2rem;
  /* 32px */
  --space-10: 2.5rem;
  /* 40px */
  --space-12: 3rem;
  /* 48px */
  --space-16: 4rem;
  /* 64px */
  --space-20: 5rem;
  /* 80px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  /* 4px */
  --radius-md: 0.375rem;
  /* 6px */
  --radius-lg: 0.5rem;
  /* 8px */
  --radius-xl: 0.75rem;
  /* 12px */
  --radius-2xl: 1rem;
  /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Enhanced PrimeReact Component Overrides */
.p-component {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
}

/* Input Components */
.p-inputtext {
  background-color: var(--bg-primary) !important;
  border: 2px solid var(--border-light) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-3) var(--space-4) !important;
  font-size: var(--font-size-base) !important;
  line-height: var(--line-height-normal) !important;
  transition: all var(--transition-fast) !important;
  min-height: 48px !important;
  width: 25rem;
  /* Accessibility - minimum touch target */
}

.p-inputtext:hover {
  /* border-color: var(--border-medium) !important; */
}

.p-inputtext:focus {
  /* border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-color-light) !important; */
  outline: none !important;
}

.p-inputtext:disabled {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-disabled) !important;
  border-color: var(--border-light) !important;
  cursor: not-allowed !important;
}

/* Button Components */
.p-button {
  background-color: #515151;
  /* border: 2px solid var(--primary-color) !important; */
  border-radius: var(--radius-lg) !important;
  padding: var(--space-3) var(--space-6) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-base) !important;
  line-height: var(--line-height-normal) !important;
  transition: all var(--transition-fast) !important;
  min-height: 50px !important;
  /* Accessibility - minimum touch target */
  cursor: pointer !important;
  border: none;
}

.p-button:hover:not(:disabled) {
  background-color: var(--primary-color-hover) !important;
  /* border-color: var(--primary-color-hover) !important; */
  transform: translateY(-1px) !important;
  /* box-shadow: var(--shadow-md) !important; */
}

.p-button:active:not(:disabled) {
  transform: translateY(0) !important;
  /* box-shadow: var(--shadow-sm) !important; */
}

.p-button:focus {
  box-shadow: none !important;
  outline: none !important;
}

.p-button:disabled {
  background-color: var(--gray-300) !important;
  border-color: var(--gray-300) !important;
  color: var(--text-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Secondary Button */
.p-button.p-button-secondary {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-medium) !important;
  color: var(--text-primary) !important;
}

.p-button.p-button-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-dark) !important;
}

/* Text Button */
.p-button.p-button-text {
  background-color: transparent !important;
  border-color: transparent !important;
  color: var(--primary-color) !important;
  padding: var(--space-2) var(--space-4) !important;
}

.p-button.p-button-text:hover:not(:disabled) {
  background-color: var(--primary-color-light) !important;
  color: var(--primary-color-hover) !important;
}

/* Dropdown Components */
.p-dropdown {
  background-color: var(--bg-primary) !important;
  border: 2px solid var(--border-light) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-lg) !important;
  min-height: 48px !important;
  transition: all var(--transition-fast) !important;
}

.p-dropdown:hover {
  border-color: var(--border-medium) !important;
}

.p-dropdown:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-color-light) !important;
  outline: none !important;
}

.p-dropdown-panel {
  background-color: var(--bg-primary) !important;
  border: 2px solid var(--border-light) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  margin-top: var(--space-1) !important;
}

.p-dropdown-item {
  color: var(--text-primary) !important;
  padding: var(--space-3) var(--space-4) !important;
  transition: all var(--transition-fast) !important;
}

.p-dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
}

.p-dropdown-item.p-highlight {
  background-color: var(--primary-color) !important;
  color: var(--bg-primary) !important;
}

/* Card Components */
.p-card {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-light) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  color: var(--text-primary) !important;
  padding: var(--space-6) !important;
  transition: all var(--transition-normal) !important;
}

.p-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.p-card .p-card-title {
  font-size: var(--font-size-xl) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
  margin-bottom: var(--space-4) !important;
}

.p-card .p-card-content {
  padding: 0 !important;
  color: var(--text-secondary) !important;
  line-height: var(--line-height-relaxed) !important;
}

/* Toolbar Components */
.p-toolbar {
  background-color: var(--bg-primary) !important;
  border: none !important;
  border-bottom: 1px solid var(--border-light) !important;
  padding: var(--space-4) var(--space-6) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Password outer wrapper */
.p-password {
  width: 100% !important;
  display: flex;
  flex-direction: column;
}

/* Make the inner .p-icon-field stretch fully */
.p-password .p-icon-field {
  width: 100% !important;
  display: flex;
  align-items: center;
}

/* Password input field */
.p-password input {
  background-color: var(--bg-primary) !important;
  border: 2px solid var(--border-light) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-3) var(--space-4) !important;
  min-height: 48px !important;
  transition: all var(--transition-fast) !important;
  flex: 1 1 auto;
  width: 100% !important;
  min-width: 0;
}

.p-password input:hover {
  border-color: var(--border-medium) !important;
}

.p-password input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-color-light) !important;
  outline: none !important;
}

/* Message Components */
.p-message {
  border-radius: var(--radius-lg) !important;
  padding: var(--space-4) !important;
  margin: var(--space-2) 0 !important;
  border-width: 0 !important;
  border-left: 4px solid !important;
}

.p-message.p-message-error {
  background-color: var(--error-light) !important;
  color: var(--error-color) !important;
  border-left-color: var(--error-color) !important;
}

.p-message.p-message-success {
  background-color: var(--success-light) !important;
  color: var(--success-color) !important;
  border-left-color: var(--success-color) !important;
}

.p-message.p-message-warn {
  background-color: var(--warning-light) !important;
  color: var(--warning-color) !important;
  border-left-color: var(--warning-color) !important;
}

.p-message.p-message-info {
  background-color: var(--info-light) !important;
  color: var(--info-color) !important;
  border-left-color: var(--info-color) !important;
}

/* Loading and Progress Components */
.p-progress-spinner {
  width: 2rem !important;
  height: 2rem !important;
}

.p-progress-spinner .p-progress-spinner-circle {
  stroke: var(--primary-color) !important;
  animation: p-progress-spinner-rotate 2s linear infinite !important;
}

/* Utility Classes */
.surface-card {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-light) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md);
  padding: var(--space-6);
}

.surface-section {
  background-color: var(--bg-primary) !important;
  padding: var(--space-8) !important;
}

.surface-overlay {
  background-color: var(--bg-overlay) !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Focus and Accessibility Improvements */
*:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  :root {
    --space-8: 1.5rem;
    --space-10: 2rem;
    --space-12: 2.5rem;
    --space-16: 3rem;
    --space-20: 4rem;
  }

  .text-display-large {
    font-size: var(--font-size-3xl);
  }

  .text-display-medium {
    font-size: var(--font-size-2xl);
  }

  .p-button {
    min-height: 44px !important;
  }

  .p-inputtext {
    min-height: 44px !important;
  }

  /* Mobile layout - stack vertically */
  .avatar-layout-horizontal {
    flex-direction: column !important;
    height: auto !important;
  }

  .avatar-video-section {
    width: 100% !important;
    height: 50vh !important;
    min-height: 300px !important;
    /* Ensure minimum height for avatar visibility */
  }

  .avatar-conversation-section {
    width: 100% !important;
    height: 50vh !important;
  }
}

@media (max-width: 480px) {
  :root {
    --space-6: 1rem;
    --space-8: 1.25rem;
    --space-10: 1.5rem;
  }

  .text-display-medium {
    font-size: var(--font-size-xl);
  }

  .text-heading-large {
    font-size: var(--font-size-lg);
  }
}

/* Avatar responsive positioning */
@media (max-width: 1200px) {

  /* Adjust avatar positioning for medium screens */
  .avatar-video-section {
    min-height: 350px !important;
  }
}

@media (max-width: 768px) {

  /* Mobile-specific avatar adjustments */
  .avatar-video-section {
    min-height: 300px !important;
  }
}

/* Ensure avatar container scales with page zoom */
.avatar-video-section {
  /* Use viewport units for responsive scaling */
  max-width: 100vw;
  /* Maintain aspect ratio */
  aspect-ratio: 16/9;
  /* Smooth scaling transitions */
  transition: all 0.3s ease;
}

li {
  list-style-type: square;
  margin-left: 30px;
  padding: 2px;
}

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  animation-fill-mode: both;
  animation: bblFadInOut 1.8s infinite ease-in-out;
}

.loader {
  color: #1b84ff;
  /* color: #fff; */
  font-size: 7px;
  position: relative;
  text-indent: -9999em;
  transform: translateZ(0);
  animation-delay: -0.16s;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  top: 0;
}

.loader:before {
  left: -3.5em;
  animation-delay: -0.32s;
}

.loader:after {
  left: 3.5em;
}

@keyframes bblFadInOut {

  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }

  40% {
    box-shadow: 0 2.5em 0 0;
  }
}

.custom-input.p-inputtext {
  padding: 0 !important;
  padding-left: 2.5rem !important;
  width: 25rem;
}

.breadcrumb-list.p-breadcrumb li {
  list-style-type: none !important;
  margin-left: 10px !important;
}

.custom-table .p-datatable-thead>tr>th {
  color: #515151;
  font-weight: 700;
  font-size: 16px;
  padding: 1rem;
}

.custom-table .p-datatable-tbody>tr>td {
  color: #515151;
  font-weight: 500;
  font-size: 16px;
  padding: 1rem;
}

.custom-table .p-datatable-header {
  border: 1px solid #5151511a;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}

.custom-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
  color: #515151 !important;
  font-size: 14px;
  font-weight: 400;
}

.custom-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text:focus {
  border: none;
  outline: none !important;
}

.custom-breadcrumb .p-breadcrumb-list li .p-menuitem-link {
  border: none;
  outline: none !important;
}

.custom-breadcrumb .p-breadcrumb-list li {
  margin-left: 5px !important;
}

.p-breadcrumb {
  background: none;
}

.custom-table .p-datatable-wrapper {
  border: 1px solid #5151511a;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  overflow: hidden;
}

.custom-table .p-datatable-thead>tr>th:not(:last-child),
.custom-table .p-datatable-tbody>tr>td:not(:last-child) {
  border-right: 1px solid #5151511a;
}

.custom-table .p-datatable-tbody>tr:not(:last-child) {
  border-bottom: 1px solid #5151511a;
}

.custom-table .p-datatable-header,
.custom-table .p-datatable-footer {
  border: none;
}

.custom-table .p-datatable {
  border-collapse: collapse;
}