import React from "react";

import { useVoiceChat } from "../logic/useVoiceChat";
import { Button } from "../Button";
import { LoadingIcon, MicIcon, MicOffIcon } from "../Icons";
import { useConversationState } from "../logic/useConversationState";
import style from "../../styles/commonStyle.module.css";
import clsx from "clsx";

export const AudioInput: React.FC = () => {
  const {
    muteInputAudio,
    unmuteInputAudio,
    isMuted,
    isVoiceChatLoading,
    micPermissionGranted,
    micPermissionDenied,
    requestMicrophonePermission,
  } = useVoiceChat();
  const { isUserTalking } = useConversationState();

  const handleMuteClick = async () => {
    // If microphone permission is denied, request it first
    if (micPermissionDenied || !micPermissionGranted) {
      const granted = await requestMicrophonePermission();
      if (!granted) {
        console.log("Cannot unmute: Microphone permission denied");
        return;
      }
    }

    if (isMuted) {
      unmuteInputAudio();
    } else {
      muteInputAudio();
    }
  };

  // Determine button state and styling
  const getButtonState = () => {
    if (isVoiceChatLoading) return "loading";
    if (micPermissionDenied) return "denied";
    if (!micPermissionGranted) return "pending";
    if (isMuted) return "muted";
    return "active";
  };

  const buttonState = getButtonState();

  return (
    <div>
      <Button
        className={clsx(`!p-2 relative`, style.micButton, {
          [style.micButtonDenied]: buttonState === "denied",
          [style.micButtonPending]: buttonState === "pending",
        })}
        disabled={isVoiceChatLoading}
        onClick={handleMuteClick}
        title={
          buttonState === "denied"
            ? "Microphone access denied. Click to request permission."
            : buttonState === "pending"
            ? "Click to enable microphone"
            : buttonState === "muted"
            ? "Click to unmute microphone"
            : "Click to mute microphone"
        }
      >
        {/* <div
          className={`absolute left-0 top-0 rounded-lg border-2 border-[#7559FF] w-full h-full ${
            isUserTalking ? "animate-ping" : ""
          }`}
        /> */}
        {isVoiceChatLoading ? (
          <LoadingIcon className="animate-spin" size={20} />
        ) : buttonState === "denied" ? (
          <MicOffIcon size={20} style={{ color: "#ef4444" }} />
        ) : buttonState === "pending" ? (
          <MicOffIcon size={20} style={{ color: "#f59e0b" }} />
        ) : isMuted ? (
          <MicOffIcon size={20} />
        ) : (
          <MicIcon size={20} />
        )}
      </Button>

      {/* Permission status indicator */}
      {(buttonState === "denied" || buttonState === "pending") && (
        <div
          className="text-xs mt-1 text-center"
          style={{
            color: buttonState === "denied" ? "#ef4444" : "#f59e0b",
            fontSize: "10px",
          }}
        >
          {buttonState === "denied" ? "Mic Denied" : "Mic Needed"}
        </div>
      )}
    </div>
  );
};
