import { useCallback, useState } from "react";

import { useStreamingAvatarContext } from "./context";

export const useVoiceChat = () => {
  const {
    avatarRef,
    isMuted,
    setIsMuted,
    isVoiceChatActive,
    setIsVoiceChatActive,
    isVoiceChatLoading,
    setIsVoiceChatLoading,
  } = useStreamingAvatarContext();

  const [micPermissionGranted, setMicPermissionGranted] = useState(false);
  const [micPermissionDenied, setMicPermissionDenied] = useState(false);

  // Check microphone permissions
  const checkMicrophonePermission = useCallback(async () => {
    try {
      const result = await navigator.permissions.query({
        name: "microphone" as PermissionName,
      });
      return result.state === "granted";
    } catch (error) {
      // Fallback: try to access microphone directly
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        stream.getTracks().forEach((track) => track.stop()); // Clean up
        return true;
      } catch (micError) {
        console.error("Microphone access denied:", micError);
        return false;
      }
    }
  }, []);

  // Request microphone permission
  const requestMicrophonePermission = useCallback(async () => {
    try {
      console.log("🎤 Requesting microphone permission...");
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop()); // Clean up immediately
      setMicPermissionGranted(true);
      setMicPermissionDenied(false);
      console.log("✅ Microphone permission granted");
      return true;
    } catch (error) {
      console.error("❌ Microphone permission denied:", error);
      setMicPermissionGranted(false);
      setMicPermissionDenied(true);
      return false;
    }
  }, []);

  const startVoiceChat = useCallback(
    async (isInputAudioMuted?: boolean) => {
      if (!avatarRef.current) return;

      setIsVoiceChatLoading(true);

      try {
        // Check if we already have microphone permission
        let hasPermission = micPermissionGranted;

        if (!hasPermission) {
          console.log("🔍 Checking microphone permission...");
          hasPermission = await checkMicrophonePermission();
        }

        // If we don't have permission, request it
        if (!hasPermission) {
          hasPermission = await requestMicrophonePermission();
        }

        if (!hasPermission) {
          console.error(
            "Cannot start voice chat: Microphone permission denied"
          );
          setIsVoiceChatLoading(false);
          return;
        }

        console.log("🎤 Starting voice chat with microphone permission...");
        await avatarRef.current?.startVoiceChat({
          isInputAudioMuted,
        });

        setIsVoiceChatActive(true);
        setIsMuted(!!isInputAudioMuted);
        console.log("✅ Voice chat started successfully");
      } catch (error) {
        console.error("Error starting voice chat:", error);
      } finally {
        setIsVoiceChatLoading(false);
      }
    },
    [
      avatarRef,
      setIsMuted,
      setIsVoiceChatActive,
      setIsVoiceChatLoading,
      micPermissionGranted,
      checkMicrophonePermission,
      requestMicrophonePermission,
    ]
  );

  const stopVoiceChat = useCallback(() => {
    if (!avatarRef.current) return;
    avatarRef.current?.closeVoiceChat();
    setIsVoiceChatActive(false);
    setIsMuted(true);
  }, [avatarRef, setIsMuted, setIsVoiceChatActive]);

  const muteInputAudio = useCallback(() => {
    if (!avatarRef.current) return;
    avatarRef.current?.muteInputAudio();
    setIsMuted(true);
  }, [avatarRef, setIsMuted]);

  const unmuteInputAudio = useCallback(() => {
    if (!avatarRef.current) return;
    avatarRef.current?.unmuteInputAudio();
    setIsMuted(false);
  }, [avatarRef, setIsMuted]);

  return {
    startVoiceChat,
    stopVoiceChat,
    muteInputAudio,
    unmuteInputAudio,
    isMuted,
    isVoiceChatActive,
    isVoiceChatLoading,
    micPermissionGranted,
    micPermissionDenied,
    requestMicrophonePermission,
  };
};
